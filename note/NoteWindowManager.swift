//
//  NoteWindowManager.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import SwiftUI
import SwiftData
import AppKit

@MainActor
class NoteWindowManager: ObservableObject {
    static let shared = NoteWindowManager()

    @Published var noteWindows: [UUID: NSWindow] = [:]
    var modelContainer: ModelContainer?
    
    private init() {}
    
    func setModelContainer(_ container: ModelContainer) {
        self.modelContainer = container
    }
    
    func createNoteWindow(for note: Note) {
        guard let container = modelContainer else {
            print("Model container not set")
            return
        }
        
        // 如果窗口已存在，直接显示
        if let existingWindow = noteWindows[note.id] {
            existingWindow.makeKeyAndOrderFront(nil)
            return
        }
        
        // 创建便签视图
        let noteView = StickyNoteView(note: note)
            .modelContainer(container)
        
        // 创建窗口内容
        let hostingView = NSHostingView(rootView: noteView)
        
        // 创建自定义窗口
        let window = NoteWindow(
            contentRect: NSRect(x: note.x, y: note.y, width: note.width, height: note.height),
            styleMask: [.borderless, .resizable, .fullSizeContentView],
            backing: .buffered,
            defer: false
        )

        window.contentView = hostingView
        window.isOpaque = false
        window.backgroundColor = NSColor.clear
        window.hasShadow = true

        // 置底设置 - 使用normal level但通过collectionBehavior实现置底效果
        window.level = .normal
        window.collectionBehavior = [.canJoinAllSpaces, .stationary, .ignoresCycle, .fullScreenAuxiliary]

        // 确保窗口可以接收事件
        window.ignoresMouseEvents = false
        window.acceptsMouseMovedEvents = true
        
        // 设置窗口关闭回调
        window.noteId = note.id
        window.windowManager = self
        
        // 保存窗口引用
        noteWindows[note.id] = window
        
        // 显示窗口
        window.makeKeyAndOrderFront(nil)
    }
    
    func closeNoteWindow(for noteId: UUID) {
        if let window = noteWindows[noteId] {
            window.close()
            noteWindows.removeValue(forKey: noteId)
        }
    }
    
    func closeAllWindows() {
        for window in noteWindows.values {
            window.close()
        }
        noteWindows.removeAll()
    }
    
    func loadExistingNotes() {
        guard let container = modelContainer else { return }
        
        let context = ModelContext(container)
        let fetchDescriptor = FetchDescriptor<Note>()
        
        do {
            let notes = try context.fetch(fetchDescriptor)
            for note in notes {
                createNoteWindow(for: note)
            }
        } catch {
            print("Failed to load existing notes: \(error)")
        }
    }
    
    func createNewNote(at position: CGPoint? = nil) {
        guard let container = modelContainer else { return }
        
        let context = ModelContext(container)
        
        // 计算新便签位置
        let x = position?.x ?? 100 + Double(noteWindows.count * 20)
        let y = position?.y ?? 100 + Double(noteWindows.count * 20)
        
        let newNote = Note(
            content: "新便签",
            x: x,
            y: y,
            width: 200,
            height: 150
        )
        
        context.insert(newNote)
        
        do {
            try context.save()
            createNoteWindow(for: newNote)
        } catch {
            print("Failed to create new note: \(error)")
        }
    }
}
