//
//  NoteWindow.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import AppKit
import SwiftUI

class NoteWindow: NSWindow {
    var noteId: UUID?
    weak var windowManager: NoteWindowManager?
    
    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: contentRect, styleMask: style, backing: backingStoreType, defer: flag)
        
        setupWindow()
    }
    
    private func setupWindow() {
        // 设置窗口属性 - 完全无边框
        self.isMovableByWindowBackground = true
        self.titlebarAppearsTransparent = true
        self.titleVisibility = .hidden
        self.standardWindowButton(.closeButton)?.isHidden = true
        self.standardWindowButton(.miniaturizeButton)?.isHidden = true
        self.standardWindowButton(.zoomButton)?.isHidden = true

        // 设置窗口行为
        self.acceptsMouseMovedEvents = true
        self.ignoresMouseEvents = false

        // 设置最小尺寸
        self.minSize = NSSize(width: 100, height: 80)
        self.maxSize = NSSize(width: 800, height: 600)

        // 置底设置 - 使用normal level但通过collectionBehavior实现置底效果
        self.level = .normal
        self.collectionBehavior = [.canJoinAllSpaces, .stationary, .ignoresCycle, .fullScreenAuxiliary]

        // 设置窗口样式 - 移除标题栏
        self.styleMask.remove(.titled)
        self.styleMask.insert(.fullSizeContentView)

        // 确保窗口不会被系统自动管理
        self.isReleasedWhenClosed = false

        // 监听窗口层级变化，确保始终保持在合适位置
        NotificationCenter.default.addObserver(
            forName: NSWindow.didBecomeMainNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.adjustWindowLevel()
        }
    }
    
    override func close() {
        // 通知窗口管理器窗口即将关闭
        if let noteId = noteId {
            windowManager?.noteWindows.removeValue(forKey: noteId)
        }
        super.close()
    }
    
    // 允许窗口成为主窗口
    override var canBecomeMain: Bool {
        return true
    }
    
    override var canBecomeKey: Bool {
        return true
    }
    
    // 处理窗口移动，更新Note模型中的位置
    override func setFrameOrigin(_ point: NSPoint) {
        super.setFrameOrigin(point)
        updateNotePosition()
    }
    
    // 处理窗口大小改变，更新Note模型中的尺寸
    override func setFrame(_ frameRect: NSRect, display flag: Bool) {
        super.setFrame(frameRect, display: flag)
        updateNotePosition()
        updateNoteSize()
    }
    
    private func updateNotePosition() {
        // 位置更新将通过SwiftUI的绑定机制处理
        // 这里暂时留空，后续会通过便签视图的绑定来更新
    }

    private func updateNoteSize() {
        // 大小更新将通过SwiftUI的绑定机制处理
        // 这里暂时留空，后续会通过便签视图的绑定来更新
    }

    private func adjustWindowLevel() {
        // 动态调整窗口层级，确保便签始终在合适位置
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }

            // 如果有其他应用窗口成为主窗口，将便签设置为floating
            if let mainWindow = NSApp.mainWindow, mainWindow != self {
                self.level = .floating
            } else {
                // 否则保持normal level
                self.level = .normal
            }

            // 确保窗口始终可见
            self.orderFront(nil)
        }
    }
}
