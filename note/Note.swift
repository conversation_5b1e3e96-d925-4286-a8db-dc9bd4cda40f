//
//  Note.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import Foundation
import SwiftData
import SwiftUI

@Model
final class Note {
    var id: UUID
    var content: String
    var x: Double
    var y: Double
    var width: Double
    var height: Double
    var colorName: String
    var isPinned: Bool
    var createdAt: Date
    var updatedAt: Date

    init(content: String = "", x: Double = 100, y: Double = 100, width: Double = 200, height: Double = 150, colorName: String = "yellow") {
        self.id = UUID()
        self.content = content
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.colorName = colorName
        self.isPinned = false
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    var color: Color {
        switch colorName {
        case "yellow":
            return Color.yellow.opacity(0.8)
        case "green":
            return Color.green.opacity(0.8)
        case "blue":
            return Color.blue.opacity(0.8)
        case "pink":
            return Color.pink.opacity(0.8)
        case "orange":
            return Color.orange.opacity(0.8)
        case "purple":
            return Color.purple.opacity(0.8)
        default:
            return Color.yellow.opacity(0.8)
        }
    }

    static let availableColors = ["yellow", "green", "blue", "pink", "orange", "purple"]
}
