//
//  StickyNoteView.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import SwiftUI
import SwiftData

struct StickyNoteView: View {
    @Bindable var note: Note
    @Environment(\.modelContext) private var modelContext
    @State private var isHovering = false
    @State private var isEditing = false
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        ZStack {
            // 便签背景
            RoundedRectangle(cornerRadius: 12)
                .fill(note.color)
                .shadow(color: .black.opacity(0.3), radius: 8, x: 3, y: 3)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.black.opacity(0.1), lineWidth: 1)
                )
            
            VStack(spacing: 0) {
                // 拖拽手柄区域
                HStack {
                    Spacer()
                    Image(systemName: "line.3.horizontal")
                        .font(.caption2)
                        .foregroundColor(.black.opacity(0.3))
                        .padding(.top, 6)
                        .padding(.trailing, 8)
                }
                .frame(height: 16)
                .contentShape(Rectangle())

                // 便签内容区域
                if isEditing {
                    TextEditor(text: $note.content)
                        .focused($isTextFieldFocused)
                        .font(.system(size: 14))
                        .scrollContentBackground(.hidden)
                        .background(Color.clear)
                        .padding(12)
                        .onSubmit {
                            saveNote()
                        }
                } else {
                    ScrollView(showsIndicators: false) {
                        Text(note.content.isEmpty ? "点击编辑..." : note.content)
                            .font(.system(size: 14))
                            .foregroundColor(note.content.isEmpty ? .gray : .black)
                            .frame(maxWidth: .infinity, alignment: .topLeading)
                            .padding(12)
                    }
                    .onTapGesture {
                        startEditing()
                    }
                }
                
                // Hover时显示的操作按钮
                if isHovering {
                    HStack(spacing: 12) {
                        // 新建便签按钮
                        Button(action: createNewNote) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.blue)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .help("新建便签")

                        Spacer()

                        // 颜色选择按钮
                        Button(action: toggleColor) {
                            Image(systemName: "paintbrush.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.orange)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .help("更改颜色")

                        // 删除按钮
                        Button(action: deleteNote) {
                            Image(systemName: "trash.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.red)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .help("删除便签")
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.ultraThinMaterial)
                            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                    )
                    .padding(.bottom, 4)
                }
            }
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
        .onTapGesture(count: 2) {
            startEditing()
        }
        .onChange(of: isTextFieldFocused) { _, focused in
            if !focused && isEditing {
                saveNote()
            }
        }
    }
    
    private func startEditing() {
        isEditing = true
        isTextFieldFocused = true
    }
    
    private func saveNote() {
        isEditing = false
        isTextFieldFocused = false
        note.updatedAt = Date()
        
        do {
            try modelContext.save()
        } catch {
            print("Failed to save note: \(error)")
        }
    }
    
    private func createNewNote() {
        // 在当前便签附近创建新便签
        let newX = note.x + 20
        let newY = note.y + 20
        
        NoteWindowManager.shared.createNewNote(at: CGPoint(x: newX, y: newY))
    }
    
    private func toggleColor() {
        let currentIndex = Note.availableColors.firstIndex(of: note.colorName) ?? 0
        let nextIndex = (currentIndex + 1) % Note.availableColors.count
        note.colorName = Note.availableColors[nextIndex]
        note.updatedAt = Date()
        
        do {
            try modelContext.save()
        } catch {
            print("Failed to update note color: \(error)")
        }
    }
    
    private func deleteNote() {
        // 关闭窗口
        NoteWindowManager.shared.closeNoteWindow(for: note.id)
        
        // 删除数据
        modelContext.delete(note)
        
        do {
            try modelContext.save()
        } catch {
            print("Failed to delete note: \(error)")
        }
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: Note.self, configurations: config)
    let note = Note(content: "这是一个示例便签", x: 100, y: 100)

    StickyNoteView(note: note)
        .modelContainer(container)
        .frame(width: 200, height: 150)
}
