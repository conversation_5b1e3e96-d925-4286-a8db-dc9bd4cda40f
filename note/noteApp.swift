//
//  noteApp.swift
//  note
//
//  Created by Mac2024 on 2025/8/5.
//

import SwiftUI
import SwiftData

@main
struct noteApp: App {
    @StateObject private var windowManager = NoteWindowManager.shared

    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            Note.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    var body: some Scene {
        // 主控制窗口（隐藏的）
        WindowGroup("NoteControl") {
            NoteControlView()
                .frame(width: 0, height: 0)
                .hidden()
        }
        .windowStyle(.hiddenTitleBar)
        .windowResizability(.contentSize)
        .modelContainer(sharedModelContainer)
        .defaultSize(width: 0, height: 0)
    }

    init() {
        // 设置窗口管理器的模型容器
        windowManager.setModelContainer(sharedModelContainer)

        // 延迟加载现有便签，确保应用完全启动后再创建窗口
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [windowManager] in
            windowManager.loadExistingNotes()

            // 如果没有现有便签，创建一个默认便签
            if windowManager.noteWindows.isEmpty {
                windowManager.createNewNote()
            }
        }
    }
}
